import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.spatial.distance import pdist, squareform
import random
from collections import defaultdict

class DNASequenceAligner:
    def __init__(self):
        self.nucleotides = ['A', 'T', 'G', 'C']
        self.substitution_matrix = {
            ('A', 'A'): 2, ('A', 'T'): -1, ('A', 'G'): -1, ('A', 'C'): -1,
            ('T', 'A'): -1, ('T', 'T'): 2, ('T', 'G'): -1, ('T', 'C'): -1,
            ('G', 'A'): -1, ('G', 'T'): -1, ('G', 'G'): 2, ('G', 'C'): -1,
            ('C', 'A'): -1, ('C', 'T'): -1, ('C', 'G'): -1, ('C', 'C'): 2
        }
        self.gap_penalty = -2
        
    def generate_sequence(self, length, gc_content=0.5):
        """Generate a random DNA sequence with specified GC content"""
        # GC content affects sequence complexity
        gc_count = int(length * gc_content)
        at_count = length - gc_count
        
        sequence = (['G', 'C'] * (gc_count // 2) + ['G'] * (gc_count % 2) +
                   ['A', 'T'] * (at_count // 2) + ['A'] * (at_count % 2))
        random.shuffle(sequence)
        return ''.join(sequence)
    
    def introduce_mutations(self, sequence, mutation_rate=0.1):
        """Introduce random mutations into a sequence"""
        mutated = list(sequence)
        for i in range(len(mutated)):
            if random.random() < mutation_rate:
                # Substitution mutation
                original = mutated[i]
                possible = [n for n in self.nucleotides if n != original]
                mutated[i] = random.choice(possible)
        return ''.join(mutated)
    
    def needleman_wunsch(self, seq1, seq2):
        """Global alignment using Needleman-Wunsch algorithm"""
        m, n = len(seq1), len(seq2)
        
        # Initialize scoring matrix
        score_matrix = np.zeros((m + 1, n + 1))
        traceback_matrix = np.zeros((m + 1, n + 1), dtype=int)
        
        # Initialize first row and column
        for i in range(1, m + 1):
            score_matrix[i][0] = i * self.gap_penalty
            traceback_matrix[i][0] = 1  # Up
        for j in range(1, n + 1):
            score_matrix[0][j] = j * self.gap_penalty
            traceback_matrix[0][j] = 2  # Left
        
        # Fill the matrix
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                match_score = score_matrix[i-1][j-1] + self.substitution_matrix[(seq1[i-1], seq2[j-1])]
                delete_score = score_matrix[i-1][j] + self.gap_penalty
                insert_score = score_matrix[i][j-1] + self.gap_penalty
                
                score_matrix[i][j] = max(match_score, delete_score, insert_score)
                
                if score_matrix[i][j] == match_score:
                    traceback_matrix[i][j] = 0  # Diagonal
                elif score_matrix[i][j] == delete_score:
                    traceback_matrix[i][j] = 1  # Up
                else:
                    traceback_matrix[i][j] = 2  # Left
        
        return score_matrix, traceback_matrix
    
    def smith_waterman(self, seq1, seq2):
        """Local alignment using Smith-Waterman algorithm"""
        m, n = len(seq1), len(seq2)
        
        # Initialize scoring matrix
        score_matrix = np.zeros((m + 1, n + 1))
        traceback_matrix = np.zeros((m + 1, n + 1), dtype=int)
        
        max_score = 0
        max_pos = (0, 0)
        
        # Fill the matrix
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                match_score = score_matrix[i-1][j-1] + self.substitution_matrix[(seq1[i-1], seq2[j-1])]
                delete_score = score_matrix[i-1][j] + self.gap_penalty
                insert_score = score_matrix[i][j-1] + self.gap_penalty
                
                score_matrix[i][j] = max(0, match_score, delete_score, insert_score)
                
                if score_matrix[i][j] > max_score:
                    max_score = score_matrix[i][j]
                    max_pos = (i, j)
                
                if score_matrix[i][j] == 0:
                    traceback_matrix[i][j] = -1  # Stop
                elif score_matrix[i][j] == match_score:
                    traceback_matrix[i][j] = 0  # Diagonal
                elif score_matrix[i][j] == delete_score:
                    traceback_matrix[i][j] = 1  # Up
                else:
                    traceback_matrix[i][j] = 2  # Left
        
        return score_matrix, traceback_matrix, max_pos
    
    def get_alignment_path(self, traceback_matrix, start_pos=None):
        """Extract alignment path from traceback matrix"""
        if start_pos is None:
            i, j = traceback_matrix.shape[0] - 1, traceback_matrix.shape[1] - 1
        else:
            i, j = start_pos
        
        path = []
        while i > 0 or j > 0:
            if traceback_matrix[i][j] == -1:  # Stop (for Smith-Waterman)
                break
            path.append((i, j))
            if traceback_matrix[i][j] == 0:  # Diagonal
                i, j = i - 1, j - 1
            elif traceback_matrix[i][j] == 1:  # Up
                i = i - 1
            else:  # Left
                j = j - 1
        
        return list(reversed(path))

def comprehensive_alignment_analysis():
    """Comprehensive analysis of DNA sequence alignment algorithms"""
    
    # Set random seed for reproducibility
    random.seed(42)
    np.random.seed(42)
    
    aligner = DNASequenceAligner()
    
    # Generate test sequences with different characteristics
    sequences = {
        'Reference': 'ATGCGATCGAATTCGCTAGCATGCTAGCTAG',
        'Similar_90': aligner.introduce_mutations('ATGCGATCGAATTCGCTAGCATGCTAGCTAG', 0.1),
        'Similar_70': aligner.introduce_mutations('ATGCGATCGAATTCGCTAGCATGCTAGCTAG', 0.3),
        'GC_Rich': aligner.generate_sequence(30, gc_content=0.8),
        'AT_Rich': aligner.generate_sequence(30, gc_content=0.2),
        'Random': aligner.generate_sequence(30, gc_content=0.5)
    }
    
    # Create comprehensive visualization
    fig = plt.figure(figsize=(20, 16))
    
    # 1. Sequence composition analysis
    ax1 = plt.subplot(3, 4, 1)
    seq_names = list(sequences.keys())
    compositions = []
    
    for seq_name, seq in sequences.items():
        comp = {nt: seq.count(nt) / len(seq) for nt in aligner.nucleotides}
        compositions.append([comp[nt] for nt in aligner.nucleotides])
    
    compositions = np.array(compositions)
    
    # Create stacked bar chart
    bottom = np.zeros(len(seq_names))
    colors = ['red', 'blue', 'green', 'orange']
    
    for i, nt in enumerate(aligner.nucleotides):
        ax1.bar(seq_names, compositions[:, i], bottom=bottom, 
               label=nt, color=colors[i], alpha=0.7)
        bottom += compositions[:, i]
    
    ax1.set_title('Nucleotide Composition')
    ax1.set_ylabel('Frequency')
    ax1.legend()
    ax1.tick_params(axis='x', rotation=45)
    
    # 2. Pairwise alignment scoring matrix heatmap
    ax2 = plt.subplot(3, 4, 2)
    
    ref_seq = sequences['Reference']
    similar_seq = sequences['Similar_70']
    
    score_matrix, traceback_matrix = aligner.needleman_wunsch(ref_seq, similar_seq)
    
    im = ax2.imshow(score_matrix, cmap='RdYlBu_r', aspect='auto')
    ax2.set_title('Needleman-Wunsch Scoring Matrix')
    ax2.set_xlabel('Reference Sequence Position')
    ax2.set_ylabel('Query Sequence Position')
    plt.colorbar(im, ax=ax2, shrink=0.8)
    
    # Overlay optimal path
    path = aligner.get_alignment_path(traceback_matrix)
    if path:
        path_i, path_j = zip(*path)
        ax2.plot(np.array(path_j) - 1, np.array(path_i) - 1, 'white', linewidth=2)
    
    # 3. Local vs Global alignment comparison
    ax3 = plt.subplot(3, 4, 3)
    
    # Smith-Waterman for local alignment
    sw_score_matrix, sw_traceback, sw_max_pos = aligner.smith_waterman(ref_seq, similar_seq)
    
    # Compare matrices
    diff_matrix = score_matrix - sw_score_matrix
    im = ax3.imshow(diff_matrix, cmap='RdBu', aspect='auto')
    ax3.set_title('Global vs Local Alignment\n(NW - SW Score Difference)')
    ax3.set_xlabel('Reference Position')
    ax3.set_ylabel('Query Position')
    plt.colorbar(im, ax=ax3, shrink=0.8)
    
    # 4. Multiple sequence alignment similarity matrix
    ax4 = plt.subplot(3, 4, 4)
    
    # Calculate pairwise similarities
    seq_list = list(sequences.values())
    seq_names_list = list(sequences.keys())
    n_seqs = len(seq_list)
    
    similarity_matrix = np.zeros((n_seqs, n_seqs))
    
    for i in range(n_seqs):
        for j in range(n_seqs):
            if i == j:
                similarity_matrix[i][j] = 1.0
            else:
                score_mat, _ = aligner.needleman_wunsch(seq_list[i], seq_list[j])
                max_possible = min(len(seq_list[i]), len(seq_list[j])) * 2  # Max score
                similarity_matrix[i][j] = score_mat[-1, -1] / max_possible
    
    im = ax4.imshow(similarity_matrix, cmap='viridis', vmin=0, vmax=1)
    ax4.set_title('Sequence Similarity Matrix')
    ax4.set_xticks(range(n_seqs))
    ax4.set_yticks(range(n_seqs))
    ax4.set_xticklabels(seq_names_list, rotation=45)
    ax4.set_yticklabels(seq_names_list)
    plt.colorbar(im, ax=ax4, shrink=0.8)
    
    # Add similarity values as text
    for i in range(n_seqs):
        for j in range(n_seqs):
            ax4.text(j, i, f'{similarity_matrix[i][j]:.2f}', 
                    ha='center', va='center', color='white' if similarity_matrix[i][j] < 0.5 else 'black')
    
    # 5. Phylogenetic tree based on sequence distances
    ax5 = plt.subplot(3, 4, 5)
    
    # Convert similarity to distance
    distance_matrix = 1 - similarity_matrix
    
    # Perform hierarchical clustering
    condensed_distances = squareform(distance_matrix)
    linkage_matrix = linkage(condensed_distances, method='ward')
    
    dendrogram(linkage_matrix, labels=seq_names_list, ax=ax5, orientation='left')
    ax5.set_title('Phylogenetic Tree\n(Based on Sequence Distance)')
    
    # 6. Gap penalty sensitivity analysis
    ax6 = plt.subplot(3, 4, 6)
    
    gap_penalties = [-1, -2, -3, -4, -5]
    alignment_scores = []
    
    for gap_penalty in gap_penalties:
        original_penalty = aligner.gap_penalty
        aligner.gap_penalty = gap_penalty
        
        score_mat, _ = aligner.needleman_wunsch(ref_seq, similar_seq)
        alignment_scores.append(score_mat[-1, -1])
        
        aligner.gap_penalty = original_penalty  # Reset
    
    ax6.plot(gap_penalties, alignment_scores, 'bo-', linewidth=2, markersize=8)
    ax6.set_title('Gap Penalty Sensitivity')
    ax6.set_xlabel('Gap Penalty')
    ax6.set_ylabel('Alignment Score')
    ax6.grid(True, alpha=0.3)
    
    # 7. Sequence motif detection
    ax7 = plt.subplot(3, 4, 7)
    
    # Find common subsequences
    motif_length = 4
    motif_counts = defaultdict(int)
    
    for seq in seq_list:
        for i in range(len(seq) - motif_length + 1):
            motif = seq[i:i + motif_length]
            motif_counts[motif] += 1
    
    # Get top motifs
    top_motifs = sorted(motif_counts.items(), key=lambda x: x[1], reverse=True)[:10]
    motifs, counts = zip(*top_motifs)
    
    ax7.bar(range(len(motifs)), counts, color='skyblue', alpha=0.7)
    ax7.set_title(f'Top {motif_length}-mer Motifs')
    ax7.set_xlabel('Motif Rank')
    ax7.set_ylabel('Frequency')
    ax7.set_xticks(range(len(motifs)))
    ax7.set_xticklabels(motifs, rotation=45)
    
    # 8. Sliding window alignment quality
    ax8 = plt.subplot(3, 4, 8)
    
    window_size = 10
    step_size = 2
    ref_seq = sequences['Reference']
    query_seq = sequences['Similar_70']
    
    positions = []
    local_scores = []
    
    for i in range(0, len(ref_seq) - window_size + 1, step_size):
        ref_window = ref_seq[i:i + window_size]
        query_window = query_seq[i:i + window_size] if i + window_size <= len(query_seq) else query_seq[i:]
        
        if len(query_window) == window_size:
            score_mat, _ = aligner.needleman_wunsch(ref_window, query_window)
            positions.append(i + window_size // 2)
            local_scores.append(score_mat[-1, -1])
    
    ax8.plot(positions, local_scores, 'g-', linewidth=2, marker='o')
    ax8.set_title(f'Sliding Window Alignment Quality\n(Window Size: {window_size})')
    ax8.set_xlabel('Sequence Position')
    ax8.set_ylabel('Local Alignment Score')
    ax8.grid(True, alpha=0.3)
    
    # 9. Substitution matrix visualization
    ax9 = plt.subplot(3, 4, 9)
    
    # Create matrix from substitution dictionary
    matrix_data = np.zeros((4, 4))
    for i, nt1 in enumerate(aligner.nucleotides):
        for j, nt2 in enumerate(aligner.nucleotides):
            matrix_data[i][j] = aligner.substitution_matrix[(nt1, nt2)]
    
    im = ax9.imshow(matrix_data, cmap='RdBu_r', vmin=-1, vmax=2)
    ax9.set_title('Nucleotide Substitution Matrix')
    ax9.set_xticks(range(4))
    ax9.set_yticks(range(4))
    ax9.set_xticklabels(aligner.nucleotides)
    ax9.set_yticklabels(aligner.nucleotides)
    
    # Add values as text
    for i in range(4):
        for j in range(4):
            ax9.text(j, i, f'{matrix_data[i][j]:.0f}', 
                    ha='center', va='center', 
                    color='white' if matrix_data[i][j] < 0 else 'black')
    
    plt.colorbar(im, ax=ax9, shrink=0.8)
    
    # 10. Performance comparison: Different sequence lengths
    ax10 = plt.subplot(3, 4, 10)
    
    seq_lengths = [10, 20, 30, 50, 80, 100]
    nw_times = []
    sw_times = []
    
    for length in seq_lengths:
        test_seq1 = aligner.generate_sequence(length)
        test_seq2 = aligner.introduce_mutations(test_seq1, 0.2)
        
        # Time Needleman-Wunsch
        import time
        start_time = time.time()
        for _ in range(10):  # Average over multiple runs
            aligner.needleman_wunsch(test_seq1, test_seq2)
        nw_time = (time.time() - start_time) / 10
        nw_times.append(nw_time * 1000)  # Convert to ms
        
        # Time Smith-Waterman
        start_time = time.time()
        for _ in range(10):
            aligner.smith_waterman(test_seq1, test_seq2)
        sw_time = (time.time() - start_time) / 10
        sw_times.append(sw_time * 1000)
    
    ax10.plot(seq_lengths, nw_times, 'bo-', label='Needleman-Wunsch', linewidth=2)
    ax10.plot(seq_lengths, sw_times, 'ro-', label='Smith-Waterman', linewidth=2)
    ax10.set_title('Algorithm Performance vs Sequence Length')
    ax10.set_xlabel('Sequence Length')
    ax10.set_ylabel('Time (ms)')
    ax10.legend()
    ax10.grid(True, alpha=0.3)
    ax10.set_yscale('log')
    
    # 11. Mutation rate vs alignment score
    ax11 = plt.subplot(3, 4, 11)
    
    mutation_rates = np.linspace(0, 0.5, 11)
    alignment_scores = []
    base_seq = sequences['Reference']
    
    for rate in mutation_rates:
        mutated_seq = aligner.introduce_mutations(base_seq, rate)
        score_mat, _ = aligner.needleman_wunsch(base_seq, mutated_seq)
        alignment_scores.append(score_mat[-1, -1])
    
    ax11.plot(mutation_rates, alignment_scores, 'go-', linewidth=2, markersize=6)
    ax11.set_title('Mutation Rate vs Alignment Score')
    ax11.set_xlabel('Mutation Rate')
    ax11.set_ylabel('Alignment Score')
    ax11.grid(True, alpha=0.3)
    
    # 12. Local alignment hotspot identification
    ax12 = plt.subplot(3, 4, 12)
    
    # Create heatmap of local alignment scores across sequence
    ref_seq = sequences['Reference']
    query_seq = sequences['GC_Rich']
    
    sw_matrix, _, _ = aligner.smith_waterman(ref_seq, query_seq)
    
    im = ax12.imshow(sw_matrix, cmap='hot', aspect='auto')
    ax12.set_title('Local Alignment Hotspots\n(Smith-Waterman Matrix)')
    ax12.set_xlabel('Reference Position')
    ax12.set_ylabel('Query Position')
    
    # Mark the maximum score position
    max_pos = np.unravel_index(np.argmax(sw_matrix), sw_matrix.shape)
    ax12.plot(max_pos[1], max_pos[0], 'wo', markersize=10, markeredgecolor='black')
    
    plt.colorbar(im, ax=ax12, shrink=0.8)
    
    plt.tight_layout()
    plt.show()
    
    # Print detailed analysis
    print("\n=== DNA Sequence Alignment Analysis Summary ===")
    print(f"Test Sequences ({len(sequences)} total):")
    for name, seq in sequences.items():
        gc_content = (seq.count('G') + seq.count('C')) / len(seq)
        print(f"  {name}: Length={len(seq)}, GC%={gc_content:.1%}")
    
    print(f"\nAlignment Statistics:")
    ref_seq = sequences['Reference']
    for name, seq in sequences.items():
        if name != 'Reference':
            score_mat, _ = aligner.needleman_wunsch(ref_seq, seq)
            print(f"  Reference vs {name}: Score={score_mat[-1, -1]:.1f}")
    
    print(f"\nTop motifs found:")
    for motif, count in top_motifs[:5]:
        print(f"  {motif}: {count} occurrences")

if __name__ == "__main__":
    comprehensive_alignment_analysis() 